"""
Improved utilities for SpARE library.

This module provides enhanced utility functions for model loading,
configuration validation, logging setup, and general helper functions.
"""

import os
import json
import logging
import warnings
from typing import Optional, Dict, Any, Tuple, Union, List
from pathlib import Path
import torch
from transformers import AutoTokenizer, AutoModel, PreTrainedModel, PreTrainedTokenizer

from .core.config import SAEConfig, ModelConfig
from .core.model_loader import UniversalModelLoader

# Project directory
PROJ_DIR = Path(os.environ.get("PROJ_DIR", "./"))


def setup_logging(
    level: Union[str, int] = logging.INFO,
    format_string: Optional[str] = None,
    log_file: Optional[str] = None,
) -> logging.Logger:
    """
    Setup logging configuration for SpARE.

    Args:
        level: Logging level (DEBUG, INFO, WARNING, ERROR)
        format_string: Custom format string for log messages
        log_file: Optional file to write logs to

    Returns:
        Configured logger instance
    """
    if format_string is None:
        format_string = "%(asctime)s - %(levelname)s %(name)s %(lineno)s: %(message)s"

    # Configure root logger
    logging.basicConfig(
        level=level,
        format=format_string,
        datefmt="%m/%d/%Y %H:%M:%S",
    )

    logger = logging.getLogger("spare")
    logger.setLevel(level)

    # Add file handler if specified
    if log_file is not None:
        add_file_handler(logger, os.path.dirname(log_file), os.path.basename(log_file))

    return logger


def add_file_handler(logger: logging.Logger, output_dir: str, file_name: str) -> None:
    """
    Add file handler to logger.

    Args:
        logger: Logger instance
        output_dir: Directory to save log file
        file_name: Name of log file
    """
    os.makedirs(output_dir, exist_ok=True)
    file_handler = logging.FileHandler(os.path.join(output_dir, file_name))
    file_handler.setLevel(logging.INFO)
    file_handler.setFormatter(
        logging.Formatter(
            "%(asctime)s - %(levelname)s %(name)s %(lineno)s: %(message)s"
        )
    )
    logger.addHandler(file_handler)


def load_jsonl(path: Union[str, Path]) -> List[Dict[str, Any]]:
    """
    Load data from JSONL file.

    Args:
        path: Path to JSONL file

    Returns:
        List of dictionaries from JSONL file
    """
    with open(path, "r", encoding="utf-8") as f:
        return [json.loads(line.strip()) for line in f if line.strip()]


def save_jsonl(data: List[Dict[str, Any]], path: Union[str, Path]) -> None:
    """
    Save data to JSONL file.

    Args:
        data: List of dictionaries to save
        path: Path to save JSONL file
    """
    with open(path, "w", encoding="utf-8") as f:
        for item in data:
            f.write(json.dumps(item) + "\n")


def load_model(model_path, flash_attn, not_return_model=False):
    tokenizer = AutoTokenizer.from_pretrained(
        model_path,
        padding_side="left",
        truncation_side="left",
    )
    tokenizer.pad_token = tokenizer.eos_token
    attn_implementation = "flash_attention_2" if flash_attn else "eager"
    print(f"attn_implementation = {attn_implementation}")
    if not_return_model:
        model = None
    else:
        if "gemma" in model_path.lower():
            model = Gemma2ForCausalLM.from_pretrained(
                model_path,
                attn_implementation=attn_implementation,
                torch_dtype=torch.bfloat16,
            )
        else:
            model = LlamaForCausalLM.from_pretrained(
                model_path,
                attn_implementation=attn_implementation,
                torch_dtype=torch.bfloat16,
            )
        model.cuda().eval()
    return model, tokenizer


def init_frozen_language_model(model_path, attn_imp="flash_attention_2"):
    bf16 = torch.bfloat16
    if "llama" in model_path.lower():
        model = LlamaForCausalLM.from_pretrained(
            model_path, attn_implementation=attn_imp, torch_dtype=bf16
        )
    elif "gemma" in model_path:
        model = Gemma2ForCausalLM.from_pretrained(
            model_path, attn_implementation=attn_imp, torch_dtype=bf16
        )
    else:
        raise NotImplementedError
    model.cuda().eval()
    for pn, p in model.named_parameters():
        p.requires_grad = False
    tokenizer = AutoTokenizer.from_pretrained(
        model_path,
        padding_side="left",
        truncation_side="left",
    )
    tokenizer.pad_token = tokenizer.eos_token
    return model, tokenizer


def load_model_and_tokenizer(
    model_name: str,
    use_flash_attention: bool = True,
    torch_dtype: Union[str, torch.dtype] = "auto",
    device_map: str = "auto",
    **kwargs,
) -> Tuple[PreTrainedModel, PreTrainedTokenizer]:
    """
    Load model and tokenizer using the universal loader.

    Args:
        model_name: HuggingFace model name or path
        use_flash_attention: Whether to use Flash Attention 2
        torch_dtype: Data type for model weights
        device_map: Device mapping strategy
        **kwargs: Additional arguments for model loading

    Returns:
        Tuple of (model, tokenizer)
    """
    # Create model config
    model_config = ModelConfig(
        model_name=model_name,
        use_flash_attention=use_flash_attention,
        torch_dtype=torch_dtype,
        device_map=device_map,
        **kwargs,
    )

    # Load using universal loader
    loader = UniversalModelLoader(model_config)
    return loader.load_model_and_tokenizer()


def validate_config(config: SAEConfig) -> List[str]:
    """
    Validate SAE configuration and return list of issues.

    Args:
        config: SAE configuration to validate

    Returns:
        List of validation error messages (empty if valid)
    """
    issues = []

    # Validate model configuration
    if not config.model.model_name:
        issues.append("Model name is required")

    # Validate dataset configuration
    if not config.dataset.dataset_name:
        issues.append("Dataset name is required")

    if config.dataset.max_seq_length <= 0:
        issues.append("Max sequence length must be positive")

    # Validate SAE architecture
    if config.d_in is not None and config.d_in <= 0:
        issues.append("d_in must be positive")

    if config.expansion_factor <= 0:
        issues.append("Expansion factor must be positive")

    if config.d_sae is not None and config.d_sae <= 0:
        issues.append("d_sae must be positive")

    # Validate training configuration
    if config.training.total_training_tokens <= 0:
        issues.append("Total training tokens must be positive")

    if config.training.batch_size <= 0:
        issues.append("Batch size must be positive")

    if config.training.learning_rate <= 0:
        issues.append("Learning rate must be positive")

    if config.training.l1_coefficient < 0:
        issues.append("L1 coefficient must be non-negative")

    # Validate hook configuration
    if config.hook_layer < 0:
        issues.append("Hook layer must be non-negative")

    return issues


# Legacy function compatibility (deprecated)
def load_model(model_path: str, flash_attn: bool, not_return_model: bool = False):
    """
    Legacy model loading function (deprecated).

    Use load_model_and_tokenizer() instead.
    """
    warnings.warn(
        "load_model() is deprecated. Use load_model_and_tokenizer() instead.",
        DeprecationWarning,
        stacklevel=2,
    )

    if not_return_model:
        tokenizer = AutoTokenizer.from_pretrained(model_path)
        if tokenizer.pad_token is None:
            tokenizer.pad_token = tokenizer.eos_token
        return None, tokenizer
    else:
        return load_model_and_tokenizer(
            model_path,
            use_flash_attention=flash_attn,
        )


def init_frozen_language_model(model_path: str, attn_imp: str = "flash_attention_2"):
    """
    Legacy function for loading frozen models (deprecated).

    Use load_model_and_tokenizer() instead.
    """
    warnings.warn(
        "init_frozen_language_model() is deprecated. Use load_model_and_tokenizer() instead.",
        DeprecationWarning,
        stacklevel=2,
    )

    model, tokenizer = load_model_and_tokenizer(
        model_path,
        use_flash_attention=(attn_imp == "flash_attention_2"),
        torch_dtype=torch.bfloat16,
    )

    # Freeze parameters
    for param in model.parameters():
        param.requires_grad_(False)

    return model, tokenizer


def load_frozen_sae(layer_idx: int, model_name: str):
    """
    Legacy function for loading pre-trained SAEs (deprecated).

    This function is kept for backward compatibility but should be replaced
    with the new SAE loading mechanisms in the core module.
    """
    warnings.warn(
        "load_frozen_sae() is deprecated. Use the new SAE loading mechanisms instead.",
        DeprecationWarning,
        stacklevel=2,
    )

    # Import here to avoid circular imports
    try:
        from itas.sae import Sae
        from itas.sae_lens.eleuther_sae_wrapper import EleutherSae
    except ImportError:
        raise ImportError("Legacy SAE loading requires old SAE modules")

    if model_name == "Meta-Llama-3-8B":
        sae = Sae.load_from_hub(
            "EleutherAI/sae-llama-3-8b-32x", hookpoint=f"layers.{layer_idx}"
        )
    elif model_name == "Llama-2-7b-hf":
        sae = Sae.load_from_hub(
            "yuzhaouoe/Llama2-7b-SAE", hookpoint=f"layers.{layer_idx}"
        )
    elif model_name == "gemma-2-9b":
        sae, cfg_dict, sparsity = EleutherSae.from_pretrained(
            release="gemma-scope-9b-pt-res-canonical",
            sae_id=f"layer_{layer_idx}/width_131k/canonical",
            device="cuda",
        )
    else:
        raise NotImplementedError(f"sae for {model_name}")

    for pn, p in sae.named_parameters():
        p.requires_grad = False
    sae.cuda()
    return sae
