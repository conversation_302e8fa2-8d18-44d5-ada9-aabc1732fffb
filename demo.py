"""
ITAS Demo: Comprehensive demonstration of the refactored SAE library.

This demo showcases the new generalized functionality that works with any
HuggingFace model and dataset, demonstrating the improved API and capabilities.
"""

import os
import json
import logging
from typing import Dict, Any, List, Optional
import torch
import numpy as np
from pathlib import Path

# Import the new ITAS library
import itas
from itas.core.config import SAEConfig, ModelConfig, DatasetConfig, TrainingConfig
from itas.core.model_loader import UniversalModelLoader
from itas.core.dataset_manager import DatasetManager
from itas.core.trainer import SAETrainer
from itas.analysis.function_extractor import FunctionExtractor

# Setup environment and logging
os.environ["TOKENIZERS_PARALLELISM"] = "false"
logger = itas.setup_logging(level=logging.INFO)


def create_sae_config(
    model_name: str = "microsoft/DialoGPT-medium",
    dataset_name: str = "wikitext",
    hook_layer: int = 6,
    expansion_factor: int = 32,
    architecture: str = "standard",
) -> SAEConfig:
    """
    Create a comprehensive SAE configuration for any HuggingFace model.

    Args:
        model_name: HuggingFace model identifier
        dataset_name: HuggingFace dataset identifier
        hook_layer: Layer to extract activations from
        expansion_factor: SAE expansion factor
        architecture: SAE architecture type

    Returns:
        Complete SAE configuration
    """
    logger.info(f"Creating SAE config for model: {model_name}")

    # Model configuration
    model_config = ModelConfig(
        model_name=model_name,
        use_flash_attention=True,  # Will be disabled automatically for incompatible models
        torch_dtype="bfloat16",
        trust_remote_code=False,
    )

    # Dataset configuration
    dataset_config = DatasetConfig(
        dataset_name=dataset_name,
        dataset_split="train",
        text_column="text",
        max_seq_length=2048,
        chunk_size=2048,
        streaming=False,
        dataset_kwargs=(
            {"name": "wikitext-2-raw-v1"} if dataset_name == "wikitext" else {}
        ),
    )

    # Training configuration
    training_config = TrainingConfig(
        total_training_tokens=1_000_000,  # Smaller for demo
        batch_size=2048,
        learning_rate=3e-4,
        l1_coefficient=1e-3,
        lr_scheduler="cosine",
        log_every_n_steps=50,
        eval_every_n_tokens=50_000,
        checkpoint_every_n_tokens=100_000,
        use_wandb=False,  # Disable for demo
    )

    # Main SAE configuration
    config = SAEConfig(
        model=model_config,
        dataset=dataset_config,
        training=training_config,
        architecture=architecture,
        expansion_factor=expansion_factor,
        hook_layer=hook_layer,
        hook_name="model.layers.{layer}",  # Will be formatted with layer number
        activation_fn="relu",
        normalize_decoder=True,
        prepend_bos=True,
        device="cuda" if torch.cuda.is_available() else "cpu",
        dtype="float32",
        seed=42,
    )

    return config


def train_sae_on_model(config: SAEConfig) -> itas.core.sae.TrainingSAE:
    """
    Train an SAE on any HuggingFace model using the new generalized framework.

    Args:
        config: SAE configuration

    Returns:
        Trained SAE model
    """
    logger.info("Starting SAE training with new generalized framework")

    # Validate configuration
    issues = itas.validate_config(config)
    if issues:
        logger.error(f"Configuration issues: {issues}")
        raise ValueError(f"Invalid configuration: {issues}")

    # Initialize trainer
    trainer = SAETrainer(config)

    # Train the SAE
    trained_sae = trainer.train()

    logger.info("SAE training completed successfully")
    return trained_sae


def demonstrate_universal_model_support():
    """
    Demonstrate that the new framework works with various HuggingFace models.
    """
    logger.info("Demonstrating universal model support")

    # Test different model architectures (excluding T5 due to Flash Attention issues)
    test_models = [
        "microsoft/DialoGPT-medium",  # GPT-2 based
        "distilbert-base-uncased",  # BERT based
        "facebook/opt-125m",  # OPT based
        "gpt2",  # Standard GPT-2
    ]

    for model_name in test_models:
        try:
            logger.info(f"Testing model: {model_name}")

            # Create configuration
            config = create_sae_config(
                model_name=model_name,
                dataset_name="wikitext",
                hook_layer=2,  # Use early layer for small models
                expansion_factor=8,  # Smaller for demo
            )

            # Update dataset config for wikitext
            if config.dataset.dataset_name == "wikitext":
                config.dataset.dataset_kwargs = {"name": "wikitext-2-raw-v1"}

            # Load model and get info
            model_loader = UniversalModelLoader(config.model)
            model, tokenizer = model_loader.load_model_and_tokenizer()

            # Get model information
            model_info = model_loader.get_model_info()
            logger.info(f"Model info: {model_info}")

            # Get available hook names
            hook_names = model_loader.get_hook_names()
            logger.info(f"Available hooks: {list(hook_names.keys())}")

            logger.info(f"✓ Successfully loaded {model_name}")

        except Exception as e:
            logger.warning(f"✗ Failed to load {model_name}: {e}")

    logger.info("Universal model support demonstration completed")


def demonstrate_flexible_dataset_support():
    """
    Demonstrate that the new framework works with various HuggingFace datasets.
    """
    logger.info("Demonstrating flexible dataset support")

    # Test different datasets (using ones that don't require trust_remote_code)
    test_datasets = [
        ("wikitext", "wikitext-2-raw-v1", "text"),
        ("squad", "plain_text", "context"),  # Use SQuAD as alternative
        ("imdb", None, "text"),  # IMDB movie reviews
    ]

    for dataset_name, dataset_config, text_column in test_datasets:
        try:
            logger.info(f"Testing dataset: {dataset_name}")

            # Create dataset configuration
            dataset_config_obj = DatasetConfig(
                dataset_name=dataset_name,
                dataset_split="train",
                text_column=text_column,
                max_seq_length=512,  # Smaller for demo
                chunk_size=512,
                streaming=True,  # Use streaming for large datasets
                dataset_kwargs={"name": dataset_config} if dataset_config else {},
            )

            # Create a simple tokenizer for testing
            from transformers import AutoTokenizer

            tokenizer = AutoTokenizer.from_pretrained("gpt2")
            if tokenizer.pad_token is None:
                tokenizer.pad_token = tokenizer.eos_token

            # Initialize dataset manager
            dataset_manager = DatasetManager(dataset_config_obj, tokenizer)

            # Load dataset
            dataset = dataset_manager.load_dataset()

            # Get dataset info
            dataset_info = dataset_manager.get_dataset_info()
            logger.info(f"Dataset info: {dataset_info}")

            logger.info(f"✓ Successfully loaded {dataset_name}")

        except Exception as e:
            logger.warning(f"✗ Failed to load {dataset_name}: {e}")

    logger.info("Flexible dataset support demonstration completed")


def demonstrate_sae_architectures():
    """
    Demonstrate different SAE architectures supported by the new framework.
    """
    logger.info("Demonstrating SAE architectures")

    # Test different SAE architectures
    architectures = ["standard", "gated", "jumprelu"]

    for architecture in architectures:
        try:
            logger.info(f"Testing SAE architecture: {architecture}")

            # Create configuration for this architecture
            config = create_sae_config(
                model_name="microsoft/DialoGPT-medium",
                dataset_name="wikitext",
                hook_layer=4,
                expansion_factor=8,
                architecture=architecture,
            )

            # Create SAE instance (without training for demo)
            from itas.core.sae import SAE

            sae = SAE(
                d_in=768,  # DialoGPT-medium hidden size
                d_sae=768 * 8,
                architecture=architecture,
                activation_fn="relu",
                normalize_decoder=True,
                device="cpu",  # Use CPU for demo
            )

            # Test forward pass
            test_input = torch.randn(10, 768)
            output = sae(test_input)

            logger.info(
                f"✓ {architecture} SAE - Input: {test_input.shape}, Output: {output.sae_out.shape}"
            )
            logger.info(
                f"  Sparsity: {output.sparsity.item():.4f}, FVU: {output.fvu.item():.4f}"
            )

        except Exception as e:
            logger.warning(f"✗ Failed to test {architecture} SAE: {e}")

    logger.info("SAE architectures demonstration completed")


def demonstrate_function_extraction():
    """
    Demonstrate the improved function extraction capabilities.
    """
    logger.info("Demonstrating function extraction")

    try:
        # Create a simple SAE for demonstration
        from itas.core.sae import SAE

        sae = SAE(
            d_in=512,
            d_sae=512 * 16,
            architecture="standard",
            activation_fn="relu",
            normalize_decoder=True,
            device="cpu",
        )

        # Create function extractor
        function_extractor = FunctionExtractor(
            sae=sae,
            initialization_method="uniform",
            regularization_strength=1e-5,
            device="cpu",
        )

        # Generate sample data
        context_activations = torch.randn(20, 512)
        target_activations = context_activations + 0.1 * torch.randn(20, 512)

        # Extract function
        result = function_extractor.extract_function(
            target_activations=target_activations,
            context_activations=context_activations,
            learning_rate=1e-2,
            num_iterations=100,
            verbose=False,
        )

        logger.info(f"✓ Function extraction completed")
        logger.info(f"  Active features: {len(result.active_features)}")
        logger.info(f"  Extraction strength: {result.extraction_strength:.6f}")
        logger.info(f"  Final loss: {result.metadata['final_loss']:.6f}")

        # Analyze feature importance
        importance_stats = function_extractor.analyze_feature_importance()
        logger.info(f"  Feature importance stats: {importance_stats}")

    except Exception as e:
        logger.warning(f"✗ Function extraction demonstration failed: {e}")

    logger.info("Function extraction demonstration completed")


def demonstrate_end_to_end_workflow():
    """
    Demonstrate a complete end-to-end workflow with the new framework.
    """
    logger.info("Demonstrating end-to-end workflow")

    try:
        # 1. Create configuration
        logger.info("Step 1: Creating SAE configuration")
        config = create_sae_config(
            model_name="microsoft/DialoGPT-medium",
            dataset_name="wikitext",
            hook_layer=6,
            expansion_factor=16,
            architecture="standard",
        )

        # Update dataset config for wikitext
        if config.dataset.dataset_name == "wikitext":
            config.dataset.dataset_kwargs = {"name": "wikitext-2-raw-v1"}

        # 2. Validate configuration
        logger.info("Step 2: Validating configuration")
        issues = itas.validate_config(config)
        if issues:
            logger.warning(f"Configuration issues found: {issues}")
        else:
            logger.info("✓ Configuration is valid")

        # 3. Load model and tokenizer
        logger.info("Step 3: Loading model and tokenizer")
        model_loader = UniversalModelLoader(config.model)
        model, tokenizer = model_loader.load_model_and_tokenizer()
        model_info = model_loader.get_model_info()
        logger.info(f"✓ Loaded model: {model_info['model_name']}")

        # 4. Setup dataset
        logger.info("Step 4: Setting up dataset")
        dataset_manager = DatasetManager(config.dataset, tokenizer)
        dataset = dataset_manager.load_dataset()
        processed_dataset = dataset_manager.preprocess_dataset()
        dataset_info = dataset_manager.get_dataset_info()
        logger.info(f"✓ Processed dataset: {dataset_info}")

        # 5. Create and test SAE
        logger.info("Step 5: Creating SAE")
        from itas.core.sae import SAE

        sae = SAE(
            d_in=model_info["hidden_size"],
            d_sae=model_info["hidden_size"] * config.expansion_factor,
            architecture=config.architecture,
            activation_fn=config.activation_fn,
            normalize_decoder=config.normalize_decoder,
            device="cpu",  # Use CPU for demo
        )

        # Test SAE with sample data
        test_input = torch.randn(5, model_info["hidden_size"])
        output = sae(test_input)
        logger.info(f"✓ SAE test - Sparsity: {output.sparsity.item():.4f}")

        # 6. Demonstrate function extraction
        logger.info("Step 6: Testing function extraction")
        function_extractor = FunctionExtractor(
            sae=sae,
            device="cpu",
        )

        # Generate sample activations
        context_acts = torch.randn(10, model_info["hidden_size"])
        target_acts = context_acts + 0.05 * torch.randn(10, model_info["hidden_size"])

        # Extract function (quick demo)
        result = function_extractor.extract_function(
            target_activations=target_acts,
            context_activations=context_acts,
            learning_rate=1e-2,
            num_iterations=50,
            verbose=False,
        )

        logger.info(
            f"✓ Function extraction - Active features: {len(result.active_features)}"
        )

        logger.info("✓ End-to-end workflow completed successfully!")

        return {
            "config": config,
            "model_info": model_info,
            "dataset_info": dataset_info,
            "sae_output": output,
            "function_result": result,
        }

    except Exception as e:
        logger.error(f"✗ End-to-end workflow failed: {e}")
        return None


def run_comprehensive_demo():
    """
    Run a comprehensive demonstration of all SpARE capabilities.
    """
    logger.info("=" * 60)
    logger.info("ITAS Comprehensive Demo - Generalized SAE Framework")
    logger.info("=" * 60)

    # Demonstrate universal model support
    logger.info("\n" + "=" * 40)
    logger.info("1. Universal Model Support")
    logger.info("=" * 40)
    demonstrate_universal_model_support()

    # Demonstrate flexible dataset support
    logger.info("\n" + "=" * 40)
    logger.info("2. Flexible Dataset Support")
    logger.info("=" * 40)
    demonstrate_flexible_dataset_support()

    # Demonstrate SAE architectures
    logger.info("\n" + "=" * 40)
    logger.info("3. SAE Architectures")
    logger.info("=" * 40)
    demonstrate_sae_architectures()

    # Demonstrate function extraction
    logger.info("\n" + "=" * 40)
    logger.info("4. Function Extraction")
    logger.info("=" * 40)
    demonstrate_function_extraction()

    # Demonstrate end-to-end workflow
    logger.info("\n" + "=" * 40)
    logger.info("5. End-to-End Workflow")
    logger.info("=" * 40)
    result = demonstrate_end_to_end_workflow()

    # Summary
    logger.info("\n" + "=" * 60)
    logger.info("Demo Summary")
    logger.info("=" * 60)

    if result:
        logger.info("✓ All demonstrations completed successfully!")
        logger.info(f"✓ Tested model: {result['model_info']['model_name']}")
        logger.info(f"✓ Processed dataset: {result['dataset_info']['dataset_name']}")
        logger.info(f"✓ SAE sparsity: {result['sae_output'].sparsity.item():.4f}")
        logger.info(
            f"✓ Function extraction: {len(result['function_result'].active_features)} active features"
        )
    else:
        logger.warning("Some demonstrations failed - check logs above")

    logger.info("\nThe new ITAS framework provides:")
    logger.info("• Universal HuggingFace model support")
    logger.info("• Flexible dataset handling")
    logger.info("• Multiple SAE architectures")
    logger.info("• Improved function extraction")
    logger.info("• Comprehensive configuration management")
    logger.info("• Better error handling and logging")

    logger.info("\n" + "=" * 60)
    logger.info("Demo completed!")
    logger.info("=" * 60)


def simple_usage_example():
    """
    Show a simple usage example of the new API.
    """
    logger.info("Simple Usage Example:")
    logger.info("-" * 30)

    # Create configuration
    config = SAEConfig(
        model=ModelConfig(model_name="microsoft/DialoGPT-medium"),
        dataset=DatasetConfig(
            dataset_name="wikitext", dataset_kwargs={"name": "wikitext-2-raw-v1"}
        ),
        training=TrainingConfig(total_training_tokens=100_000),
        hook_layer=6,
        expansion_factor=32,
    )

    # Train SAE (commented out for demo)
    # trainer = SAETrainer(config)
    # sae = trainer.train()

    logger.info("✓ Configuration created successfully")
    logger.info("✓ Ready for training with SAETrainer(config).train()")

    return config


if __name__ == "__main__":
    """
    Main demo script showcasing the new generalized SpARE framework.

    This demonstrates how the refactored library now works with any HuggingFace
    model and dataset, providing a much more flexible and powerful API.
    """

    # Run the comprehensive demo
    run_comprehensive_demo()

    # Show simple usage example
    logger.info("\n" + "=" * 40)
    logger.info("Simple Usage Example")
    logger.info("=" * 40)
    simple_usage_example()

    logger.info("\n" + "=" * 60)
    logger.info("Key Improvements in the Refactored SpARE Library:")
    logger.info("=" * 60)
    logger.info("1. ✓ Universal HuggingFace model support (any architecture)")
    logger.info("2. ✓ Flexible dataset handling (any HuggingFace dataset)")
    logger.info("3. ✓ Multiple SAE architectures (standard, gated, jumprelu)")
    logger.info("4. ✓ Improved configuration management")
    logger.info("5. ✓ Better error handling and validation")
    logger.info("6. ✓ Comprehensive logging and monitoring")
    logger.info("7. ✓ Enhanced function extraction capabilities")
    logger.info("8. ✓ Software engineering best practices")
    logger.info("9. ✓ Modular and extensible design")
    logger.info("10. ✓ Backward compatibility with legacy functions")

    logger.info("\n" + "Usage Examples:")
    logger.info("-" * 20)
    logger.info("# Basic usage:")
    logger.info("import spare")
    logger.info("config = spare.SAEConfig(model_name='any-hf-model')")
    logger.info("trainer = spare.SAETrainer(config)")
    logger.info("sae = trainer.train()")
    logger.info("")
    logger.info("# Function extraction:")
    logger.info("extractor = spare.FunctionExtractor(sae)")
    logger.info("result = extractor.extract_function(target, context)")
    logger.info("")
    logger.info("# Model loading:")
    logger.info("model, tokenizer = spare.load_model_and_tokenizer('any-hf-model')")

    logger.info("\n" + "=" * 60)
    logger.info("Thank you for using SpARE!")
    logger.info("=" * 60)
